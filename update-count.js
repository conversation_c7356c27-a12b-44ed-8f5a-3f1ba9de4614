/**
 * DrChrono Patient Lister for January 1, 2025
 *
 * This script fetches all patients from DrChrono API created on or after January 1, 2025
 * and displays them. It does NOT update the checkpoint.json file.
 *
 * Usage:
 * 1. Make sure you have previously exchanged an authorization code for tokens
 * 2. Run: node update-count.js
 */

// Load environment variables from .env file
require('dotenv').config();
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Path to the token file and checkpoint file
const tokenFilePath = path.join(__dirname, 'data', 'drchrono-token.json');
const checkpointFilePath = path.join(__dirname, 'data', 'checkpoint.json');

// Check if token file exists
if (!fs.existsSync(tokenFilePath)) {
  console.error('Error: Token file not found.');
  console.error('Please run exchange-code.js first to get the initial tokens.');
  process.exit(1);
}

// Load token data
let accessToken;
try {
  const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
  accessToken = tokenData.access_token;

  // Check if token exists
  if (!accessToken) {
    console.error('Error: Invalid token file. No access token found.');
    process.exit(1);
  }

  // Check if token is expired
  const expiryTime = new Date(tokenData.expiry_time);
  if (expiryTime < new Date()) {
    console.error('Error: Access token is expired.');
    console.error('Please run refresh-token.js to get a new access token.');
    process.exit(1);
  }

  console.log('Token loaded successfully.');
} catch (error) {
  console.error('Error loading token file:', error.message);
  process.exit(1);
}

// Function to fetch and display the first 3 patients from DrChrono API created on or after January 1, 2025
async function fetchPatientCount() {
  try {
    console.log('Fetching patients from DrChrono API created on or after January 1, 2025...');

    // Set the date to January 1, 2025
    const targetDate = '2025-01-01';

    // Make the initial request using the since parameter as recommended in the API docs
    const initialResponse = await axios.get('https://app.drchrono.com/api/patients', {
      params: {
        limit: 100, // Get 100 patients per page
        since: targetDate, // Get patients created on or after January 1, 2025
        verbose: true // Include additional data like insurance and custom demographics
      },
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    console.log('\nAPI call successful!');
    console.log(`Total patients in response: ${initialResponse.data.results.length}`);

    // Filter patients created on or after January 1, 2025 (double-check with created_at)
    let patientsAfterTargetDate = initialResponse.data.results.filter(patient => {
      if (!patient.created_at) return false;
      const createdAt = new Date(patient.created_at);
      const targetDateObj = new Date(targetDate);
      return createdAt >= targetDateObj;
    });

    console.log(`Patients created on or after ${targetDate}: ${patientsAfterTargetDate.length}`);

    // Output all patients from the first page
    if (patientsAfterTargetDate.length > 0) {
      console.log('\n=== All Patients Created On or After Jan 1, 2025 ===');

      patientsAfterTargetDate.forEach((patient, index) => {
        console.log(`\n--- Patient #${index + 1} ---`);
        console.log(JSON.stringify({
          id: patient.id,
          chart_id: patient.chart_id,
          first_name: patient.first_name,
          last_name: patient.last_name,
          date_of_birth: patient.date_of_birth,
          email: patient.email,
          cell_phone: patient.cell_phone,
          created_at: patient.created_at,
          updated_at: patient.updated_at
        }, null, 2));
      });
    } else {
      console.log('\nNo patients found created on or after January 1, 2025');
    }

    // Count total patients after target date (for information only)
    let totalCount = patientsAfterTargetDate.length;
    let nextUrl = initialResponse.data.next;

    // Fetch all remaining pages to get all patients
    while (nextUrl) {
      console.log(`\nFetching next page...`);

      const response = await axios.get(nextUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      // Filter patients created on or after January 1, 2025
      const filteredPatients = response.data.results.filter(patient => {
        if (!patient.created_at) return false;
        const createdAt = new Date(patient.created_at);
        const targetDateObj = new Date(targetDate);
        return createdAt >= targetDateObj;
      });

      // Add to total count
      totalCount += filteredPatients.length;

      // Output all patients from this page
      if (filteredPatients.length > 0) {
        filteredPatients.forEach((patient, index) => {
          console.log(`\n--- Patient #${totalCount - filteredPatients.length + index + 1} ---`);
          console.log(JSON.stringify({
            id: patient.id,
            chart_id: patient.chart_id,
            first_name: patient.first_name,
            last_name: patient.last_name,
            date_of_birth: patient.date_of_birth,
            email: patient.email,
            cell_phone: patient.cell_phone,
            created_at: patient.created_at,
            updated_at: patient.updated_at
          }, null, 2));
        });
      }

      nextUrl = response.data.next;
    }

    console.log('\nAPI calls completed successfully!');
    console.log(`Total patients found created on or after ${targetDate}: ${totalCount}`);
    console.log('\nNOTE: The count in checkpoint.json has NOT been updated.');

    return totalCount;
  } catch (error) {
    console.error('Failed to fetch patient count:');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);

      // Check for authentication errors
      if (error.response.status === 401) {
        console.error('\nAuthentication error. Your token may be expired.');
        console.error('Please run refresh-token.js to get a new access token.');
      }
    } else {
      console.error('Error:', error.message);
    }
    throw error;
  }
}

// Function to display the checkpoint file without updating it
function updateCheckpoint() {
  try {
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Load existing checkpoint file if it exists
    let checkpoint = {
      count: 0,
      lastSync: new Date().toISOString(),
      recentPatients: [],
      lastUpdated: new Date().toISOString()
    };

    if (fs.existsSync(checkpointFilePath)) {
      checkpoint = JSON.parse(fs.readFileSync(checkpointFilePath, 'utf8'));
      console.log('\nLoaded existing checkpoint file.');
      console.log('Current count:', checkpoint.count);
      console.log('Last sync date:', checkpoint.lastSync);
      console.log('Last updated:', checkpoint.lastUpdated);
    } else {
      console.log('\nNo existing checkpoint file found.');
    }

    console.log('\nCheckpoint file was NOT updated. This script only displays the first 3 patients.');
    console.log('To update the count, use the original update-count.js script.');

    return checkpoint;
  } catch (error) {
    console.error('Failed to read checkpoint file:', error.message);
    throw error;
  }
}

// Execute the script
async function main() {
  try {
    // Fetch and display the first 3 patients from DrChrono created on or after January 1, 2025
    await fetchPatientCount();

    // Display the checkpoint file without updating it
    updateCheckpoint();

    console.log('\nScript completed successfully!');
    console.log('All patients created on or after January 1, 2025 have been displayed.');
    console.log('No changes were made to the checkpoint file.');
  } catch (error) {
    console.error('Script failed:', error.message);
    process.exit(1);
  }
}

main();
