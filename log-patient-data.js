/**
 * DrChrono Patient Data Logger
 * 
 * This script fetches patient data from DrChrono and logs all available fields
 * to help understand the data structure.
 * 
 * Usage:
 * 1. Make sure you have previously exchanged an authorization code for tokens
 * 2. Run: node log-patient-data.js
 */

// Load environment variables from .env file
require('dotenv').config();
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Path to the token file
const tokenFilePath = path.join(__dirname, 'data', 'drchrono-token.json');

// Check if token file exists
if (!fs.existsSync(tokenFilePath)) {
  console.error('Error: Token file not found.');
  console.error('Please run exchange-code.js first to get the initial tokens.');
  process.exit(1);
}

// Load tokens from file
const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
const accessToken = tokenData.access_token || tokenData.accessToken;

if (!accessToken) {
  console.error('Error: No access token found in token file.');
  console.error('Please run exchange-code.js again to get new tokens.');
  process.exit(1);
}

// Check if token is expired
const expiryTime = new Date(tokenData.expiry_time || tokenData.tokenExpiry);
const now = new Date();
const bufferTime = 10 * 60 * 1000; // 10 minutes in milliseconds

if (now.getTime() > expiryTime.getTime() - bufferTime) {
  console.error('Error: Access token is expired or about to expire.');
  console.error('Please run refresh-token.js to get a new access token.');
  process.exit(1);
}

// Function to fetch patients from DrChrono API
async function fetchPatients() {
  try {
    console.log('Fetching patients from DrChrono API...');
    
    const response = await axios.get('https://app.drchrono.com/api/patients', {
      params: {
        verbose: true, // Include additional data like insurance and custom demographics
        limit: 10 // Limit to 10 patients for testing
      },
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    console.log('\nAPI call successful!');
    console.log('Total patients:', response.data.count);
    console.log('Results:', response.data.results.length);
    
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Save full response to file for inspection
    fs.writeFileSync(
      path.join(dataDir, 'patients-full-data.json'),
      JSON.stringify(response.data, null, 2),
      'utf8'
    );
    
    console.log('\nFull response saved to data/patients-full-data.json');
    
    // Process each patient to log their data structure
    if (response.data.results.length > 0) {
      // Get the first patient for detailed analysis
      const patient = response.data.results[0];
      
      // Log all available fields
      const fields = Object.keys(patient);
      console.log(`\nPatient data has ${fields.length} fields:`);
      console.log(fields);
      
      // Create a sample of the data structure
      const sampleData = {};
      fields.forEach(field => {
        const value = patient[field];
        
        // Determine the type of the field
        let type = typeof value;
        if (value === null) {
          type = 'null';
        } else if (Array.isArray(value)) {
          type = 'array';
          if (value.length > 0) {
            type += ` of ${typeof value[0]}`;
          }
        }
        
        // Add sample value (truncated if too long)
        let sampleValue = value;
        if (type === 'string' && value.length > 50) {
          sampleValue = value.substring(0, 50) + '...';
        } else if (type === 'object' && value !== null) {
          sampleValue = '[Object]';
        } else if (type.startsWith('array')) {
          sampleValue = `[Array with ${value.length} items]`;
        }
        
        sampleData[field] = {
          type,
          sample: sampleValue
        };
      });
      
      // Save the data structure to file
      fs.writeFileSync(
        path.join(dataDir, 'patient-data-structure.json'),
        JSON.stringify(sampleData, null, 2),
        'utf8'
      );
      
      console.log('\nData structure saved to data/patient-data-structure.json');
      
      // Log the specific fields we're interested in
      console.log('\nFields we are transferring to GoHighLevel:');
      console.log('First name:', patient.first_name);
      console.log('Last name:', patient.last_name);
      console.log('Birthday:', patient.date_of_birth);
      console.log('Phone number:', patient.cell_phone || patient.home_phone);
      console.log('Email address:', patient.email);
    }
    
    return response.data;
  } catch (error) {
    console.error('Failed to fetch patients:');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      
      // Check for authentication errors
      if (error.response.status === 401) {
        console.error('\nAuthentication error. Your token may be expired.');
        console.error('Please run refresh-token.js to get a new access token.');
      }
    } else {
      console.error('Error:', error.message);
    }
    throw error;
  }
}

// Execute the API call
fetchPatients()
  .then(() => {
    console.log('\nPatient data logging complete!');
    console.log('Check the data directory for detailed information.');
  })
  .catch(error => {
    console.error('API call failed:', error.message);
    process.exit(1);
  });
