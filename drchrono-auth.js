/**
 * DrChrono Authorization Script
 *
 * This script generates the authorization URL for DrChrono API and provides
 * instructions for exchanging the authorization code for tokens.
 *
 * Usage:
 * 1. Make sure you have a .env file with DRCHRONO_CLIENT_ID and DRCHRONO_CLIENT_SECRET
 * 2. Run: node drchrono-auth.js
 * 3. Copy the authorization URL and open it in your browser
 * 4. After authorization, you'll be redirected to the redirect URI with a code parameter
 * 5. Copy the code and use it with exchange-code.js to get your tokens
 */

// Load environment variables from .env file
require('dotenv').config();

// ======== CONFIGURATION ========
// Get credentials from environment variables
const CLIENT_ID = process.env.DRCHRONO_CLIENT_ID;
const CLIENT_SECRET = process.env.DRCHRONO_CLIENT_SECRET;
const REDIRECT_URI = process.env.DRCHRONO_REDIRECT_URI || 'https://virilityinc.com/confirmed/';
const SCOPES = ['patients:read', 'patients:write', 'clinical:read'];

// Check if credentials are available
if (!CLIENT_ID || !CLIENT_SECRET) {
  console.error('Error: Missing DrChrono credentials in environment variables.');
  console.error('Please make sure you have DRCHRONO_CLIENT_ID and DRCHRONO_CLIENT_SECRET in your .env file.');
  process.exit(1);
}

// ======== STEP 1: AUTHORIZATION URL ========
// Generate the authorization URL
const authorizationUrl = `https://drchrono.com/o/authorize/?client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&response_type=code&scope=${encodeURIComponent(SCOPES.join(' '))}`;

console.log('===== DRCHRONO API AUTHORIZATION =====');
console.log('\nStep 1: Open the following URL in your browser:');
console.log('\n' + authorizationUrl);
console.log('\nThis URL will redirect you to DrChrono\'s login page.');
console.log('After logging in and authorizing the application, you will be redirected to:');
console.log(`${REDIRECT_URI}?code=YOUR_AUTHORIZATION_CODE`);
console.log('\nStep 2: Copy the authorization code from the URL (the value of the "code" parameter)');
console.log('\nStep 3: Use the authorization code with exchange-code.js to get your access tokens:');
console.log('node exchange-code.js YOUR_AUTHORIZATION_CODE');

// Try to open the URL in the default browser
try {
  console.log('\nAttempting to open the authorization URL in your default browser...');
  open(authorizationUrl);
  console.log('Browser opened with the authorization URL.');
} catch (error) {
  console.log('Could not automatically open the browser.');
  console.log('Please manually copy and paste the URL into your browser.');
}