# DrChrono to GoHighLevel Integration

This project provides an integration between DrChrono and GoHighLevel, focusing specifically on syncing the following key patient fields:

- First name
- Last name
- Birthday (date of birth)
- Phone number
- Email address

**Important Note:** This integration automatically processes patients created OR updated in the current year, capturing both new patients and returning patients with recent activity.

## Features

- OAuth authentication with DrChrono API
- Automatic syncing of patients from DrChrono to GoHighLevel
- Focused data transfer of only essential patient information
- Robust whitespace handling for all patient data fields
- Opportunity creation for contacts in GoHighLevel
- Efficient patient tracking with count and timestamp for resumable syncs
- Duplicate contact and opportunity detection
- Retry mechanism for failed contacts
- Scheduled sync, retry, full sync, and verification jobs
- REST API for triggering operations and checking status
- Automatic DrChrono token refresh
- Configurable cron job for recurring execution (every 10 minutes)

## Prerequisites

- Node.js 14.x or higher
- npm or yarn
- DrChrono API credentials (Client ID and Client Secret)
- GoHighLevel API access tokens for each location

## Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/chronogohighlevel.git
cd chronogohighlevel
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file based on the `.env.example` file:

```bash
cp .env.example .env
```

4. Edit the `.env` file with your DrChrono and GoHighLevel credentials.

## Configuration

### DrChrono Configuration

You need to set up an application in the DrChrono API dashboard:

1. Go to https://app.drchrono.com/api-management/
2. Create a new application
3. Set the redirect URI to `http://localhost:3000/oauth/callback` (or your custom URL)
4. Select the required API scopes:
   - `patients:read` - Required to access patient data
   - `patients:write` - Only if you need to create/update patients
   - `clinical:read` - If you need access to clinical data
5. Set the API Version to "v3 (Sunnyvale)" in the dropdown
6. Copy the Client ID and Client Secret to your `.env` file

The integration uses the verbose parameter (`?verbose=true`) when fetching patient data to ensure all demographic information is retrieved. It automatically uses the current year and processes patients based on both `created_at` and `updated_at` dates to capture new and returning patients.

### GoHighLevel Configuration

For each location/practice you want to sync to GoHighLevel:

1. Get the access token for the location
2. Get the location ID
3. (Optional) Get the pipeline ID and pipeline stage ID if you want to create opportunities
   - You can get these IDs by running the `test-ghl-pipelines.js` script
   - The script will output all available pipelines and stages with their IDs
4. Add these to your `.env` file following the format in `.env.example`

#### Simple Count-Based Patient Tracking

The integration uses a simple count-based tracking system that stores:
- A running count of processed patients (e.g., if count is 50, it means 50 patients have been processed)
- A timestamp of the last sync operation

This approach provides several benefits:
- Minimal disk space usage (no need to store any patient IDs)
- Resumable syncs (if count is 50, the server will start from patient 51)
- Extremely efficient for large datasets with thousands of patients

The tracking data is stored in `patient_db.json` in the root directory and is automatically maintained by the system. When the integration runs, it:
1. Automatically determines the current year
2. Fetches ALL patients from DrChrono (created OR updated in current year)
3. Processes both new patients (created this year) and returning patients (updated this year)
4. Updates the count as each patient is processed
5. Auto-generates missing email/phone for GoHighLevel compatibility

You can use the `update-count.js` script to initialize the count based on current year patient data:

```bash
node update-count.js
```

#### Data Cleaning and Whitespace Handling

The integration includes robust whitespace handling for all patient data coming from DrChrono:
- Trims whitespace from all string fields before processing
- Ensures consistent data formatting when creating contacts and opportunities
- Handles whitespace in search parameters when looking for existing contacts
- Properly formats phone numbers and email addresses for API calls

#### Creating Opportunities

If you provide pipeline and stage IDs in your configuration, the integration will automatically create opportunities in GoHighLevel for each patient. This allows you to track patients through your sales/onboarding pipeline.

The integration includes smart opportunity handling:
- Checks if a contact already exists before creating it
- Checks if a contact already has an opportunity before creating a new one
- Creates opportunities for both new contacts and existing contacts without opportunities
- Skips opportunity creation for contacts that already have opportunities

To test opportunity creation, you can use the `test-ghl-opportunities.js` script:

```bash
node test-ghl-opportunities.js
```

This script will:
1. Create a test contact in GoHighLevel
2. Fetch available pipelines if not provided
3. Check if the contact already has an opportunity
4. Create an opportunity for the contact if needed

## Usage

### Building and Running

Build the TypeScript code:

```bash
npm run build
```

Start the server:

```bash
npm start
```

For development with auto-reload:

```bash
npm run dev
```

### Authentication

#### Manual Authentication
1. Start the server
2. Visit `http://localhost:3000/auth-url` to get the DrChrono authorization URL
3. Open the URL in your browser and authorize the application
4. You will be redirected to the callback URL and the application will store the access token

#### Automatic Authentication
You can also use the automatic authentication feature:

1. Start the server with the `--auto-auth` flag:
   ```bash
   npm start -- --auto-auth
   ```
   This will automatically open the authorization URL in your default browser.

2. Alternatively, use the API endpoint:
   ```bash
   curl -X POST http://localhost:3000/auto-auth
   ```
   This will programmatically open the authorization URL in your default browser.

### API Endpoints

- `GET /status` - Check the status of the integration
- `POST /trigger` - Trigger operations (sync, retry, full-sync, verification)
- `GET /auth-url` - Get the DrChrono authorization URL
- `POST /auto-auth` - Automatically open the DrChrono authorization URL in the default browser
- `POST /refresh-token` - Manually refresh the DrChrono access token

The integration also includes automatic token refresh mechanisms:
- Proactive refresh before token expiration (10-minute buffer)
- Scheduled hourly token refresh check
- Automatic refresh when a token expires during an API call
- Automatic refresh attempt during initialization if a refresh token is available

#### Trigger Operations

```bash
curl -X POST http://localhost:3000/trigger -H "Content-Type: application/json" -d '{"operation": "sync"}'
```

Available operations:
- `sync` - Sync patients from DrChrono to GoHighLevel (created OR updated in current year)
- `retry` - Retry failed contacts
- `full-sync` - Perform a full sync of all patients (created OR updated in current year)
- `verification` - Verify contacts and opportunities

## Scheduled Jobs

The integration runs the following scheduled jobs:

- Patient sync: Every 10 minutes (configurable via `CRON_SCHEDULE` in .env)
- Failed contacts retry: Every 5 minutes (configurable via `RETRY_CRON_SCHEDULE`)
- Full sync: Daily at 2 AM (configurable via `FULL_SYNC_CRON_SCHEDULE`)
- Verification: Weekly on Sunday at 3 AM (configurable via `VERIFICATION_CRON_SCHEDULE`)
- Token refresh: Automatic refresh when token is expired or about to expire (5-minute buffer)

The fetch.js script can be run in two modes:
1. **One-time execution**: Run once and exit
2. **Cron mode**: Run immediately and then continue on a schedule (every 10 minutes by default)

To enable cron mode, set `ENABLE_CRON=true` in your .env file. When run in cron mode, the script will:
1. Perform an initial sync immediately
2. Set up a recurring job to run every 10 minutes
3. Continue running until manually stopped (Ctrl+C)

## Project Structure

```
chronogohighlevel/
├── .env                    # Environment variables
├── .env.example            # Example environment variables
├── .gitignore              # Git ignore file
├── package.json            # Project dependencies
├── tsconfig.json           # TypeScript configuration
├── src/
│   ├── config.ts           # Configuration file
│   ├── index.ts            # Main entry point
│   ├── config/             # Configuration files
│   │   └── gohighlevel-fields.ts  # GoHighLevel field mappings
│   ├── services/           # API service classes
│   │   ├── drchrono.ts     # DrChrono API service
│   │   └── gohighlevel.ts  # GoHighLevel API service
│   ├── types/              # TypeScript type definitions
│   │   ├── drchrono.ts     # DrChrono types
│   │   └── gohighlevel.ts  # GoHighLevel types
│   └── utils/              # Utility functions
│       ├── logger.ts       # Logging utility
│       ├── retry.ts        # API retry mechanism
│       └── patient-tracker.ts # Track processed patients
├── data/                   # Data storage directory
│   └── processed-patients.json # Track processed patients
└── logs/                   # Log files directory
```

## License

MIT

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
