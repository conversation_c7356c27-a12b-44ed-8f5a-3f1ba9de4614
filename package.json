{"name": "chronogohighlevel", "version": "1.0.0", "description": "Integration between DrChrono and GoHighLevel", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.6.2", "chronogohighlevel": "file:", "dotenv": "^16.3.1", "express": "^4.21.2", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}