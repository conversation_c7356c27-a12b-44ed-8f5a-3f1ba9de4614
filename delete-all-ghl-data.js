/**
 * GoHighLevel Data Cleanup Script
 * 
 * ⚠️  WARNING: This script will DELETE ALL contacts and opportunities in your GoHighLevel location!
 * ⚠️  This action is IRREVERSIBLE! Make sure you have backups if needed.
 * 
 * Usage:
 * 1. Make sure your .env file has the correct GoHighLevel credentials
 * 2. Run: node delete-all-ghl-data.js
 * 3. Follow the confirmation prompts
 * 
 * Features:
 * - Deletes ALL contacts in the location
 * - Deletes ALL opportunities in the location
 * - Multiple confirmation prompts for safety
 * - Progress tracking with detailed logging
 * - Rate limiting to respect API limits
 * - Error handling and retry logic
 */

// Load required modules
require('dotenv').config();
const axios = require('axios');

// GoHighLevel API constants
const GO_HIGHLEVEL_API_URL = process.env.GO_HIGHLEVEL_API_URL || 'https://services.leadconnectorhq.com';
const GO_HIGHLEVEL_ACCESS_TOKEN = process.env.GO_HIGHLEVEL_ACCESS_TOKEN;
const GO_HIGHLEVEL_LOCATION_ID = process.env.GO_HIGHLEVEL_LOCATION_ID;

// Check if GoHighLevel integration is enabled
const isGhlIntegrationEnabled = process.env.ENABLE_GHL_INTEGRATION === 'true';

// Validate environment variables
function validateEnvironment() {
  console.log('\n===== GOHIGHLEVEL CLEANUP CONFIGURATION =====');
  
  if (!isGhlIntegrationEnabled) {
    console.error('❌ GoHighLevel integration is DISABLED. Set ENABLE_GHL_INTEGRATION=true to enable it.');
    return false;
  }
  
  if (!GO_HIGHLEVEL_ACCESS_TOKEN) {
    console.error('❌ GO_HIGHLEVEL_ACCESS_TOKEN is required but not found in environment variables.');
    return false;
  }
  
  if (!GO_HIGHLEVEL_LOCATION_ID) {
    console.error('❌ GO_HIGHLEVEL_LOCATION_ID is required but not found in environment variables.');
    return false;
  }
  
  console.log('✅ GoHighLevel integration is ENABLED.');
  console.log(`✅ API URL: ${GO_HIGHLEVEL_API_URL}`);
  console.log(`✅ Location ID: ${GO_HIGHLEVEL_LOCATION_ID}`);
  console.log(`✅ Access Token: ${GO_HIGHLEVEL_ACCESS_TOKEN.substring(0, 10)}...`);
  console.log('=============================================\n');
  
  return true;
}

// No user input needed - script runs automatically

// Helper function to add delay between API calls
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to get all contacts
async function getAllContacts() {
  console.log('\n📋 Fetching all contacts...');
  
  const headers = {
    'Accept': 'application/json',
    'Authorization': `Bearer ${GO_HIGHLEVEL_ACCESS_TOKEN}`,
    'Content-Type': 'application/json',
    'Version': '2021-07-28'
  };
  
  let allContacts = [];
  let nextCursor = null;
  let pageCount = 0;
  
  try {
    do {
      pageCount++;
      console.log(`  Fetching contacts page ${pageCount}...`);
      
      const params = {
        locationId: GO_HIGHLEVEL_LOCATION_ID,
        limit: 100
      };
      
      if (nextCursor) {
        params.startAfter = nextCursor;
      }
      
      const response = await axios.get(`${GO_HIGHLEVEL_API_URL}/contacts/`, {
        headers,
        params
      });
      
      const contacts = response.data.contacts || [];
      allContacts = allContacts.concat(contacts);
      
      console.log(`    Found ${contacts.length} contacts on this page`);
      console.log(`    Total contacts so far: ${allContacts.length}`);
      
      nextCursor = response.data.meta?.nextCursor;
      
      // Add delay to respect rate limits
      await delay(500);
      
    } while (nextCursor);
    
    console.log(`\n✅ Total contacts found: ${allContacts.length}`);
    return allContacts;
    
  } catch (error) {
    console.error('❌ Error fetching contacts:', error.message);
    if (error.response) {
      console.error(`   Response status: ${error.response.status}`);
      console.error(`   Response data:`, error.response.data);
    }
    throw error;
  }
}

// Function to get all opportunities
async function getAllOpportunities() {
  console.log('\n📋 Fetching all opportunities...');
  
  const headers = {
    'Accept': 'application/json',
    'Authorization': `Bearer ${GO_HIGHLEVEL_ACCESS_TOKEN}`,
    'Content-Type': 'application/json',
    'Version': '2021-07-28'
  };
  
  let allOpportunities = [];
  let nextCursor = null;
  let pageCount = 0;
  
  try {
    do {
      pageCount++;
      console.log(`  Fetching opportunities page ${pageCount}...`);
      
      const params = {
        locationId: GO_HIGHLEVEL_LOCATION_ID,
        limit: 100
      };
      
      if (nextCursor) {
        params.startAfter = nextCursor;
      }
      
      const response = await axios.get(`${GO_HIGHLEVEL_API_URL}/opportunities/`, {
        headers,
        params
      });
      
      const opportunities = response.data.opportunities || [];
      allOpportunities = allOpportunities.concat(opportunities);
      
      console.log(`    Found ${opportunities.length} opportunities on this page`);
      console.log(`    Total opportunities so far: ${allOpportunities.length}`);
      
      nextCursor = response.data.meta?.nextCursor;
      
      // Add delay to respect rate limits
      await delay(500);
      
    } while (nextCursor);
    
    console.log(`\n✅ Total opportunities found: ${allOpportunities.length}`);
    return allOpportunities;
    
  } catch (error) {
    console.error('❌ Error fetching opportunities:', error.message);
    if (error.response) {
      console.error(`   Response status: ${error.response.status}`);
      console.error(`   Response data:`, error.response.data);
    }
    throw error;
  }
}

// Function to delete a single contact
async function deleteContact(contactId, contactName, index, total) {
  const headers = {
    'Accept': 'application/json',
    'Authorization': `Bearer ${GO_HIGHLEVEL_ACCESS_TOKEN}`,
    'Content-Type': 'application/json',
    'Version': '2021-07-28'
  };
  
  try {
    await axios.delete(`${GO_HIGHLEVEL_API_URL}/contacts/${contactId}`, { headers });
    console.log(`  ✅ [${index}/${total}] Deleted contact: ${contactName} (ID: ${contactId})`);
    return { success: true, id: contactId };
  } catch (error) {
    console.error(`  ❌ [${index}/${total}] Failed to delete contact: ${contactName} (ID: ${contactId})`);
    console.error(`     Error: ${error.message}`);
    return { success: false, id: contactId, error: error.message };
  }
}

// Function to delete a single opportunity
async function deleteOpportunity(opportunityId, opportunityName, index, total) {
  const headers = {
    'Accept': 'application/json',
    'Authorization': `Bearer ${GO_HIGHLEVEL_ACCESS_TOKEN}`,
    'Content-Type': 'application/json',
    'Version': '2021-07-28'
  };

  try {
    await axios.delete(`${GO_HIGHLEVEL_API_URL}/opportunities/${opportunityId}`, { headers });
    console.log(`  ✅ [${index}/${total}] Deleted opportunity: ${opportunityName} (ID: ${opportunityId})`);
    return { success: true, id: opportunityId };
  } catch (error) {
    console.error(`  ❌ [${index}/${total}] Failed to delete opportunity: ${opportunityName} (ID: ${opportunityId})`);
    console.error(`     Error: ${error.message}`);
    return { success: false, id: opportunityId, error: error.message };
  }
}

// Function to delete all contacts
async function deleteAllContacts(contacts) {
  if (contacts.length === 0) {
    console.log('\n📭 No contacts to delete.');
    return { deleted: 0, failed: 0 };
  }

  console.log(`\n🗑️  Starting deletion of ${contacts.length} contacts...`);

  let deleted = 0;
  let failed = 0;
  const failedContacts = [];

  for (let i = 0; i < contacts.length; i++) {
    const contact = contacts[i];
    const contactName = `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unknown';

    const result = await deleteContact(contact.id, contactName, i + 1, contacts.length);

    if (result.success) {
      deleted++;
    } else {
      failed++;
      failedContacts.push(result);
    }

    // Add delay between deletions to respect rate limits
    await delay(200);
  }

  console.log(`\n📊 Contact deletion summary:`);
  console.log(`   ✅ Successfully deleted: ${deleted}`);
  console.log(`   ❌ Failed to delete: ${failed}`);

  if (failedContacts.length > 0) {
    console.log(`\n❌ Failed contact deletions:`);
    failedContacts.forEach(contact => {
      console.log(`   - ID: ${contact.id}, Error: ${contact.error}`);
    });
  }

  return { deleted, failed, failedContacts };
}

// Function to delete all opportunities
async function deleteAllOpportunities(opportunities) {
  if (opportunities.length === 0) {
    console.log('\n📭 No opportunities to delete.');
    return { deleted: 0, failed: 0 };
  }

  console.log(`\n🗑️  Starting deletion of ${opportunities.length} opportunities...`);

  let deleted = 0;
  let failed = 0;
  const failedOpportunities = [];

  for (let i = 0; i < opportunities.length; i++) {
    const opportunity = opportunities[i];
    const opportunityName = opportunity.name || 'Unknown';

    const result = await deleteOpportunity(opportunity.id, opportunityName, i + 1, opportunities.length);

    if (result.success) {
      deleted++;
    } else {
      failed++;
      failedOpportunities.push(result);
    }

    // Add delay between deletions to respect rate limits
    await delay(200);
  }

  console.log(`\n📊 Opportunity deletion summary:`);
  console.log(`   ✅ Successfully deleted: ${deleted}`);
  console.log(`   ❌ Failed to delete: ${failed}`);

  if (failedOpportunities.length > 0) {
    console.log(`\n❌ Failed opportunity deletions:`);
    failedOpportunities.forEach(opportunity => {
      console.log(`   - ID: ${opportunity.id}, Error: ${opportunity.error}`);
    });
  }

  return { deleted, failed, failedOpportunities };
}

// Main execution function
async function main() {
  console.log('\n🚨 ===== GOHIGHLEVEL DATA CLEANUP SCRIPT ===== 🚨');
  console.log('🚀 Starting automatic deletion of ALL contacts and opportunities...');

  // Validate environment
  if (!validateEnvironment()) {
    console.log('\n❌ Environment validation failed. Exiting...');
    process.exit(1);
  }

  try {
    console.log('\n🚀 Starting data cleanup process...');
    const startTime = new Date();

    // Fetch all data first
    const [contacts, opportunities] = await Promise.all([
      getAllContacts(),
      getAllOpportunities()
    ]);

    // Show summary before deletion
    console.log('\n📊 DELETION SUMMARY:');
    console.log(`   Contacts to delete: ${contacts.length}`);
    console.log(`   Opportunities to delete: ${opportunities.length}`);
    console.log(`   Total items to delete: ${contacts.length + opportunities.length}`);
    console.log('\n🗑️  Starting deletion process...');

    // Delete opportunities first (they may depend on contacts)
    console.log('\n🗑️  Phase 1: Deleting opportunities...');
    const opportunityResults = await deleteAllOpportunities(opportunities);

    // Delete contacts
    console.log('\n🗑️  Phase 2: Deleting contacts...');
    const contactResults = await deleteAllContacts(contacts);

    // Final summary
    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log('\n🎉 ===== CLEANUP COMPLETED ===== 🎉');
    console.log(`⏱️  Total time: ${duration} seconds`);
    console.log('\n📊 FINAL RESULTS:');
    console.log(`   Contacts - Deleted: ${contactResults.deleted}, Failed: ${contactResults.failed}`);
    console.log(`   Opportunities - Deleted: ${opportunityResults.deleted}, Failed: ${opportunityResults.failed}`);
    console.log(`   Total deleted: ${contactResults.deleted + opportunityResults.deleted}`);
    console.log(`   Total failed: ${contactResults.failed + opportunityResults.failed}`);

    if (contactResults.failed > 0 || opportunityResults.failed > 0) {
      console.log('\n⚠️  Some deletions failed. Check the logs above for details.');
    } else {
      console.log('\n✅ All data successfully deleted!');
    }

  } catch (error) {
    console.error('\n❌ An error occurred during the cleanup process:');
    console.error(error.message);
    console.error('\n🛑 Cleanup process aborted.');
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n🛑 Process interrupted by user. Exiting...');
  process.exit(0);
});

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('\n💥 Fatal error:', error.message);
    process.exit(1);
  });
}
