/**
 * DrChrono Authorization Code Exchange
 * 
 * This script exchanges the authorization code for access and refresh tokens.
 * 
 * Usage:
 * 1. Make sure you have a .env file with DRCHRONO_CLIENT_ID and DRCHRONO_CLIENT_SECRET
 * 2. Run: node exchange-code.js YOUR_AUTHORIZATION_CODE
 */

// Load environment variables from .env file
require('dotenv').config();
const axios = require('axios');
const querystring = require('querystring');
const fs = require('fs');
const path = require('path');

// Get authorization code from command line argument
const authCode = process.argv[2];

// Get credentials from environment variables
const CLIENT_ID = process.env.DRCHRONO_CLIENT_ID;
const CLIENT_SECRET = process.env.DRCHRONO_CLIENT_SECRET;
const REDIRECT_URI = process.env.DRCHRONO_REDIRECT_URI || 'https://virilityinc.com/confirmed/';

// Check if credentials are available
if (!CLIENT_ID || !CLIENT_SECRET) {
  console.error('Error: Missing DrChrono credentials in environment variables.');
  console.error('Please make sure you have DRCHRONO_CLIENT_ID and DRCHRONO_CLIENT_SECRET in your .env file.');
  process.exit(1);
}

// Check if authorization code is provided
if (!authCode) {
  console.error('Error: No authorization code provided.');
  console.error('Usage: node exchange-code.js YOUR_AUTHORIZATION_CODE');
  process.exit(1);
}

// Function to exchange the authorization code for tokens
async function exchangeCodeForTokens(code) {
  try {
    console.log('Exchanging authorization code for tokens...');
    console.log(`Code: ${code}`);
    console.log(`Redirect URI: ${REDIRECT_URI}`);
    console.log(`Client ID: ${CLIENT_ID}`);

    const tokenUrl = 'https://drchrono.com/o/token/';
    const data = {
      code,
      grant_type: 'authorization_code',
      redirect_uri: REDIRECT_URI,
      client_id: CLIENT_ID,
      client_secret: CLIENT_SECRET
    };

    console.log('Request data:', {
      ...data,
      client_secret: '[HIDDEN]' // Don't log the secret
    });
    
    const response = await axios.post(tokenUrl, querystring.stringify(data), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    console.log('\nToken exchange successful!');
    console.log('Access Token:', response.data.access_token);
    console.log('Refresh Token:', response.data.refresh_token);
    console.log('Expires In:', response.data.expires_in, 'seconds');
    
    // Calculate expiry time
    const expiryTime = new Date(Date.now() + response.data.expires_in * 1000);
    console.log('Token Expires At:', expiryTime.toISOString());
    
    // Save tokens to a file
    const tokenData = {
      access_token: response.data.access_token,
      refresh_token: response.data.refresh_token,
      expires_in: response.data.expires_in,
      token_type: response.data.token_type,
      expiry_time: expiryTime.toISOString()
    };
    
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Save tokens to file
    fs.writeFileSync(
      path.join(dataDir, 'drchrono-token.json'),
      JSON.stringify(tokenData, null, 2),
      'utf8'
    );
    
    console.log('\nTokens saved to data/drchrono-token.json');
    console.log('\nYou can now use these tokens to make API calls to DrChrono.');
    
    return response.data;
  } catch (error) {
    console.error('Failed to exchange code for tokens:');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    throw error;
  }
}

// Execute the token exchange
exchangeCodeForTokens(authCode)
  .catch(error => {
    console.error('Token exchange failed:', error.message);
    process.exit(1);
  });
