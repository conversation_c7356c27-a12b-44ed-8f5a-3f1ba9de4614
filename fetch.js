/**
 * <PERSON><PERSON><PERSON><PERSON> to GoHighLevel Patient Sync
 *
 * This script fetches all patients from DrChrono (created OR updated in current year),
 * organized by month, and syncs them to GoHighLevel.
 *
 * Features:
 * - Automatically uses current year (dynamic)
 * - Processes both new patients (created this year) and returning patients (updated this year)
 * - Auto-generates missing email/phone for GoHighLevel compatibility
 * - Configurable to fetch from beginning of year or current month only
 *
 * Usage:
 * 1. Make sure you have a valid token in data/drchrono-token.json
 * 2. Configure your .env file with GoHighLevel credentials
 * 3. Run: node fetch.js
 */

// Load required modules
require('dotenv').config();
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const cron = require('node-cron');
const express = require('express');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Path to the token file
const tokenFilePath = path.join(__dirname, 'data', 'drchrono-token.json');

// Path to the patient count database
const patientDbPath = path.join(__dirname, 'patient_db.json');

/**
 * Function to manage the patient count database
 * Simple tracking system that only stores counts and last sync time
 */
const patientDb = {
  // Load the database from disk or create a new one if it doesn't exist
  load: () => {
    try {
      if (fs.existsSync(patientDbPath)) {
        const data = fs.readFileSync(patientDbPath, 'utf8');
        return JSON.parse(data);
      }
      // Initialize with empty structure if file doesn't exist
      return {
        lastCount: 0,
        lastSync: new Date().toISOString(),
        monthCounts: {
          January: 0,
          February: 0,
          March: 0,
          April: 0,
          May: 0,
          June: 0,
          July: 0,
          August: 0,
          September: 0,
          October: 0,
          November: 0,
          December: 0
        }
      };
    } catch (error) {
      console.error(`Error loading patient database: ${error.message}`);
      // Return empty structure on error
      return {
        lastCount: 0,
        lastSync: new Date().toISOString(),
        monthCounts: {
          January: 0,
          February: 0,
          March: 0,
          April: 0,
          May: 0,
          June: 0,
          July: 0,
          August: 0,
          September: 0,
          October: 0,
          November: 0,
          December: 0
        }
      };
    }
  },

  // Save the database to disk
  save: (db) => {
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(patientDbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // Update timestamp
      db.lastSync = new Date().toISOString();

      // Write to file
      fs.writeFileSync(patientDbPath, JSON.stringify(db, null, 2), 'utf8');
      console.log(`Patient database updated. Total processed: ${db.lastCount}`);
      return true;
    } catch (error) {
      console.error(`Error saving patient database: ${error.message}`);
      return false;
    }
  },

  // Update the count for a specific month
  updateMonthCount: (db, month, count) => {
    if (!db.monthCounts) {
      db.monthCounts = {};
    }
    db.monthCounts[month] = count;
    db.lastCount = Object.values(db.monthCounts).reduce((sum, val) => sum + val, 0);
    return db;
  },

  // Get the count for a specific month
  getMonthCount: (db, month) => {
    if (!db.monthCounts) {
      return 0;
    }
    return db.monthCounts[month] || 0;
  }
};

// GoHighLevel API constants
const GO_HIGHLEVEL_API_URL = process.env.GO_HIGHLEVEL_API_URL || 'https://services.leadconnectorhq.com';
const GO_HIGHLEVEL_ACCESS_TOKEN = process.env.GO_HIGHLEVEL_ACCESS_TOKEN;
const GO_HIGHLEVEL_LOCATION_ID = process.env.GO_HIGHLEVEL_LOCATION_ID;
const GO_HIGHLEVEL_PIPELINE_ID = process.env.GO_HIGHLEVEL_PIPELINE_ID;
const GO_HIGHLEVEL_PIPELINE_STAGE_ID = process.env.GO_HIGHLEVEL_PIPELINE_STAGE_ID;

// Check if GoHighLevel integration is enabled
const isGhlIntegrationEnabled = process.env.ENABLE_GHL_INTEGRATION === 'true';

// Check if we should fetch from beginning of year or just current month
const fetchFromBeginningOfYear = process.env.FETCH_FROM_BEGINNING_OF_YEAR !== 'false'; // Default to true

// Check if we should auto-generate missing email and phone numbers
const autoGenerateEmail = process.env.AUTO_GENERATE_EMAIL !== 'false'; // Default to true
const autoGeneratePhone = process.env.AUTO_GENERATE_PHONE !== 'false'; // Default to true

// Helper function to get current year dynamically
function getCurrentYear() {
  return new Date().getFullYear();
}

// Log the current year being used
console.log(`\n===== DRCHRONO TO GOHIGHLEVEL SYNC =====`);
console.log(`Current year: ${getCurrentYear()}`);
console.log(`Fetch from beginning of year: ${fetchFromBeginningOfYear}`);
console.log(`Auto-generate email: ${autoGenerateEmail}`);
console.log(`Auto-generate phone: ${autoGeneratePhone}`);
console.log(`GoHighLevel integration: ${isGhlIntegrationEnabled ? 'ENABLED' : 'DISABLED'}`);
console.log(`==========================================\n`);

// Validate GoHighLevel environment variables
function validateGhlEnvironmentVariables() {
  console.log('\n===== GOHIGHLEVEL INTEGRATION CONFIGURATION =====');

  if (!isGhlIntegrationEnabled) {
    console.log('GoHighLevel integration is DISABLED. Set ENABLE_GHL_INTEGRATION=true to enable it.');
    return false;
  }

  console.log('GoHighLevel integration is ENABLED.');

  let isValid = true;
  const requiredVars = [
    { name: 'GO_HIGHLEVEL_ACCESS_TOKEN', value: GO_HIGHLEVEL_ACCESS_TOKEN },
    { name: 'GO_HIGHLEVEL_LOCATION_ID', value: GO_HIGHLEVEL_LOCATION_ID }
  ];

  const optionalVars = [
    { name: 'GO_HIGHLEVEL_API_URL', value: GO_HIGHLEVEL_API_URL, default: 'https://services.leadconnectorhq.com' },
    { name: 'GO_HIGHLEVEL_PIPELINE_ID', value: GO_HIGHLEVEL_PIPELINE_ID, required: false },
    { name: 'GO_HIGHLEVEL_PIPELINE_STAGE_ID', value: GO_HIGHLEVEL_PIPELINE_STAGE_ID, required: false }
  ];

  // Check required variables
  for (const variable of requiredVars) {
    if (!variable.value) {
      console.error(`ERROR: ${variable.name} is required but not found in environment variables.`);
      isValid = false;
    } else {
      console.log(`✓ ${variable.name} is configured.`);
    }
  }

  // Check optional variables
  for (const variable of optionalVars) {
    if (variable.value) {
      console.log(`✓ ${variable.name} is configured.`);
    } else if (variable.default) {
      console.log(`i ${variable.name} is not configured, using default: ${variable.default}`);
    } else {
      console.log(`i ${variable.name} is not configured. Some features may be limited.`);
    }
  }

  // Check if pipeline variables are configured for opportunity creation
  if (GO_HIGHLEVEL_PIPELINE_ID && GO_HIGHLEVEL_PIPELINE_STAGE_ID) {
    console.log('✓ Pipeline configuration is complete. Opportunity creation is ENABLED.');
  } else {
    console.log('i Pipeline configuration is incomplete. Opportunity creation is DISABLED.');
  }

  console.log('=====================================================\n');
  return isValid;
}

// Validate GoHighLevel environment variables if integration is enabled
const isGhlConfigValid = validateGhlEnvironmentVariables();

// Helper function to generate a unique ID for custom fields
function generateCustomFieldId(prefix = '') {
  return `${prefix}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
}

// Helper function to generate a placeholder email when patient email is missing or invalid
function generatePlaceholderEmail(patient) {
  const firstName = (patient.first_name || 'patient').toLowerCase().replace(/[^a-z0-9]/g, '');
  const lastName = (patient.last_name || 'unknown').toLowerCase().replace(/[^a-z0-9]/g, '');
  const patientId = patient.id || Date.now();
  const timestamp = Date.now();

  return `${firstName}.${lastName}.${patientId}.${timestamp}@drchrono-placeholder.local`;
}

// Helper function to generate a placeholder phone number when patient phone is missing or invalid
function generatePlaceholderPhone(patient) {
  // Generate a phone number in format: 555-0000-XXXX where XXXX is based on patient ID
  const patientId = patient.id || Math.floor(Math.random() * 10000);
  const lastFourDigits = String(patientId).slice(-4).padStart(4, '0');

  return `555-0000-${lastFourDigits}`;
}

// Helper function to validate and clean email
function validateAndCleanEmail(email) {
  if (!email || typeof email !== 'string') return null;

  const cleanedEmail = email.trim();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  return emailRegex.test(cleanedEmail) ? cleanedEmail : null;
}

// Helper function to validate and clean phone number
function validateAndCleanPhone(phone) {
  if (!phone || typeof phone !== 'string') return null;

  const cleanedPhone = phone.replace(/\D/g, '');

  // Must be at least 10 digits for a valid US phone number
  return cleanedPhone.length >= 10 ? cleanedPhone : null;
}

// Check if token file exists
if (!fs.existsSync(tokenFilePath)) {
  console.error('Error: Token file not found.');
  console.error('Please run exchange-code.js first to get the initial tokens.');
  process.exit(1);
}

// Function to refresh the DrChrono access token
async function refreshDrChronoToken() {
  console.log('Refreshing DrChrono access token...');

  try {
    // Read the current token data
    const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));

    // Check if refresh token exists
    if (!tokenData.refresh_token) {
      console.error('Error: No refresh token found in token file.');
      console.error('Please run exchange-code.js to get new tokens.');
      process.exit(1);
    }

    // Make the refresh token request
    const response = await axios.post('https://drchrono.com/o/token/',
      new URLSearchParams({
        refresh_token: tokenData.refresh_token,
        grant_type: 'refresh_token',
        client_id: process.env.DRCHRONO_CLIENT_ID,
        client_secret: process.env.DRCHRONO_CLIENT_SECRET
      }).toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    // Calculate new expiry time
    const expiryTime = new Date(Date.now() + response.data.expires_in * 1000);

    // Update token data
    const newTokenData = {
      access_token: response.data.access_token,
      refresh_token: response.data.refresh_token || tokenData.refresh_token, // Use new refresh token if provided, otherwise keep the old one
      expires_in: response.data.expires_in,
      token_type: response.data.token_type,
      expiry_time: expiryTime.toISOString()
    };

    // Save updated token data
    fs.writeFileSync(tokenFilePath, JSON.stringify(newTokenData, null, 2), 'utf8');

    console.log('Token refreshed successfully!');
    console.log('New token expires at:', expiryTime.toISOString());

    return newTokenData.access_token;
  } catch (error) {
    console.error('Failed to refresh token.');
    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      // Don't log response data as it might contain sensitive information
    } else {
      console.error(`Error: ${error.message}`);
    }
    throw error;
  }
}

// Function to load and validate the DrChrono access token
async function loadDrChronoToken() {
  try {
    // Read the token file
    const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
    let accessToken = tokenData.access_token;

    // Check if token exists
    if (!accessToken) {
      console.error('Error: Invalid token file. No access token found.');
      process.exit(1);
    }

    // Check if token is expired or about to expire (within 5 minutes)
    const expiryTime = new Date(tokenData.expiry_time);
    const now = new Date();
    const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

    if (expiryTime < new Date(now.getTime() + bufferTime)) {
      console.log('Access token is expired or about to expire. Refreshing...');
      accessToken = await refreshDrChronoToken();
    } else {
      console.log('Token is valid until:', expiryTime.toISOString());
    }

    console.log('Token loaded successfully.');
    return accessToken;
  } catch (error) {
    console.error('Error loading token file:', error.message);
    process.exit(1);
  }
}

// Load the DrChrono access token
let accessToken;

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

/**
 * Helper function to check if a year is a leap year
 * @param {number} year - The year to check
 * @returns {boolean} - True if the year is a leap year, false otherwise
 */
function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * Helper function to get the month name from a date string
 * @param {string} dateString - The date string in format YYYY-MM-DD
 * @returns {string} - The month name
 */
function getMonthNameFromDate(dateString) {
  const date = new Date(dateString);
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  return monthNames[date.getMonth()];
}

/**
 * Helper function to get the month and year from a date string
 * @param {string} dateString - The date string in format YYYY-MM-DD
 * @returns {Object} - Object with month and year properties
 */
function getMonthAndYearFromDate(dateString) {
  const date = new Date(dateString);
  return {
    month: date.getMonth() + 1, // 1-12
    year: date.getFullYear()
  };
}

/**
 * Helper function to check if a patient should be processed based on created_at or updated_at
 * @param {Object} patient - The patient object
 * @param {number} targetYear - The target year to check against
 * @returns {Object} - Object with shouldProcess boolean and reason string
 */
function shouldProcessPatient(patient, targetYear) {
  const createdDate = patient.created_at ? new Date(patient.created_at) : null;
  const updatedDate = patient.updated_at ? new Date(patient.updated_at) : null;

  const createdInTargetYear = createdDate && createdDate.getFullYear() === targetYear;
  const updatedInTargetYear = updatedDate && updatedDate.getFullYear() === targetYear;

  if (createdInTargetYear && updatedInTargetYear) {
    return { shouldProcess: true, reason: 'new_and_updated', primaryDate: createdDate };
  } else if (createdInTargetYear) {
    return { shouldProcess: true, reason: 'new_patient', primaryDate: createdDate };
  } else if (updatedInTargetYear) {
    return { shouldProcess: true, reason: 'returning_patient', primaryDate: updatedDate };
  }

  return { shouldProcess: false, reason: 'not_in_target_year', primaryDate: null };
}

/**
 * Helper function to clean and normalize patient data
 * @param {Object} patient - The patient data from DrChrono API
 * @returns {Object} - The cleaned patient data
 */
function cleanPatientData(patient) {
  // Create a copy of the patient object to avoid modifying the original
  const cleanedPatient = { ...patient };

  // Trim whitespace from string fields
  Object.keys(cleanedPatient).forEach(key => {
    if (typeof cleanedPatient[key] === 'string') {
      cleanedPatient[key] = cleanedPatient[key].trim();
    }
  });

  // Ensure required fields exist and have default values if missing
  cleanedPatient.first_name = cleanedPatient.first_name || '';
  cleanedPatient.last_name = cleanedPatient.last_name || '';
  cleanedPatient.date_of_birth = cleanedPatient.date_of_birth || null;
  cleanedPatient.email = cleanedPatient.email || '';

  // Use cell_phone if available, otherwise use home_phone
  cleanedPatient.cell_phone = cleanedPatient.cell_phone || cleanedPatient.home_phone || '';

  return cleanedPatient;
}

/**
 * Helper function to map patient data to GoHighLevel custom fields
 * @param {Object} patient - The cleaned patient data
 * @returns {Array} - Array of custom fields for GoHighLevel
 */
function mapPatientToCustomFields(patient) {
  const customFields = [
    { key: 'internal_id', field_value: generateCustomFieldId('DrChrono') },
    { key: 'chart_id', field_value: patient.chart_id || '' },
    { key: 'drchrono_id', field_value: patient.id?.toString() || '' },
    { key: 'date_of_first_appointment', field_value: patient.date_of_first_appointment || '' },
    { key: 'date_of_last_appointment', field_value: patient.date_of_last_appointment || '' },
    { key: 'patient_status', field_value: patient.patient_status || '' },
    { key: 'patient_payment_profile', field_value: patient.patient_payment_profile || '' },
    { key: 'social_security_number', field_value: patient.social_security_number || '' },
    { key: 'race', field_value: patient.race || '' },
    { key: 'ethnicity', field_value: patient.ethnicity || '' },
    { key: 'preferred_language', field_value: patient.preferred_language || '' },
    { key: 'doctor_id', field_value: patient.doctor?.toString() || '' },
  ];
  return customFields.filter(field => field.field_value !== '');
}

/**
 * Helper function to format patient data for GoHighLevel compatibility
 * @param {Object} patient - The cleaned patient data
 * @returns {Object} - The formatted contact data for GoHighLevel
 */
function formatPatientForGoHighLevel(patient) {
  // Validate and clean email, generate placeholder if needed
  let email = validateAndCleanEmail(patient.email);
  let isEmailGenerated = false;
  if (!email && autoGenerateEmail) {
    email = generatePlaceholderEmail(patient);
    isEmailGenerated = true;
  } else if (!email) {
    email = '<EMAIL>'; // Fallback if auto-generation is disabled
  }

  // Validate and clean phone, generate placeholder if needed
  let phone = validateAndCleanPhone(patient.cell_phone);
  let isPhoneGenerated = false;
  if (!phone && autoGeneratePhone) {
    phone = generatePlaceholderPhone(patient);
    isPhoneGenerated = true;
  } else if (!phone) {
    phone = '555-0000-0000'; // Fallback if auto-generation is disabled
  }

  // Format the data according to GoHighLevel API requirements
  const contactData = {
    firstName: patient.first_name,
    lastName: patient.last_name,
    name: `${patient.first_name} ${patient.last_name}`,
    email: email,
    phone: phone,
    dateOfBirth: patient.date_of_birth,
    // Add any additional fields required by GoHighLevel
    source: 'DrChrono Integration',
    tags: ['DrChrono Patient'],
    gender: patient.gender === 'Male' ? 'male' : patient.gender === 'Female' ? 'female' : null,
    customFields: mapPatientToCustomFields(patient)
  };

  // Add flags to indicate generated fields
  if (isEmailGenerated) {
    contactData.tags.push('Generated Email');
    contactData.customFields.push({ key: 'email_generated', field_value: 'true' });
    contactData.customFields.push({ key: 'original_email', field_value: patient.email || 'none' });
  }

  if (isPhoneGenerated) {
    contactData.tags.push('Generated Phone');
    contactData.customFields.push({ key: 'phone_generated', field_value: 'true' });
    contactData.customFields.push({ key: 'original_phone', field_value: patient.cell_phone || patient.home_phone || 'none' });
  }

  // Add address information if available
  if (patient.address || patient.city || patient.state || patient.zip_code) {
    contactData.address1 = patient.address || '';
    contactData.city = patient.city || '';
    contactData.state = patient.state || '';
    contactData.postalCode = patient.zip_code || '';
  }

  return contactData;
}

/**
 * Helper function to check if a contact has valid information for GoHighLevel
 * @param {Object} contactData - The formatted contact data for GoHighLevel
 * @returns {boolean} - True if the contact has valid information, false otherwise
 */
function isValidForGoHighLevel(contactData) {
  // Check if the contact has a name (required)
  const hasName = contactData.firstName || contactData.lastName;

  // Since we now auto-generate email and phone when missing,
  // we just need to ensure the contact has a name
  if (!hasName) {
    return false;
  }

  // Verify that email and phone are present (they should be after formatting)
  const hasEmail = contactData.email && contactData.email.includes('@');
  const hasPhone = contactData.phone && contactData.phone.replace(/\D/g, '').length >= 10;

  // Both should be present after our formatting function
  return hasEmail && hasPhone;
}

/**
 * Create a contact in GoHighLevel
 * @param {Object} patient - The patient data to create a contact from
 * @param {Object} patientDatabase - The patient count database
 * @param {string} month - The current month being processed
 * @returns {Promise<{contactId: string|null, success: boolean}>} - The contact ID if successful
 */
async function createGoHighLevelContact(patient, patientDatabase, month) {
  // Skip if GoHighLevel integration is not enabled or configuration is invalid
  if (!isGhlIntegrationEnabled || !isGhlConfigValid) {
    return { contactId: null, success: false };
  }

  // Clean and format the patient data
  const cleanedPatient = cleanPatientData(patient);
  const contactData = formatPatientForGoHighLevel(cleanedPatient);

  // Skip if the contact doesn't have valid information
  if (!isValidForGoHighLevel(contactData)) {
    console.log(`Skipping contact creation for patient ${patient.id} due to invalid contact information`);
    return { contactId: null, success: false };
  }

  // Add the location ID to the contact data
  contactData.locationId = GO_HIGHLEVEL_LOCATION_ID;

  const headers = {
    Accept: 'application/json',
    Authorization: `Bearer ${GO_HIGHLEVEL_ACCESS_TOKEN}`,
    'Content-Type': 'application/json',
    Version: '2021-07-28',
  };

  try {
    const response = await axios.post(`${GO_HIGHLEVEL_API_URL}/contacts/`, contactData, { headers });
    const contactId = response.data.contact.id;

    // Log success with detailed information
    console.log(`[${new Date().toISOString()}] SUCCESS: Contact created successfully`);
    console.log(`Contact ID: ${contactId}`);
    console.log(`Patient: ${cleanedPatient.first_name} ${cleanedPatient.last_name} (ID: ${patient.id})`);
    console.log(`Type: ${patient._processReason === 'new_patient' ? 'New Patient' : patient._processReason === 'returning_patient' ? 'Returning Patient' : 'New & Updated Patient'}`);
    console.log(`Email: ${contactData.email}${contactData.tags.includes('Generated Email') ? ' (generated)' : ''}`);
    console.log(`Phone: ${contactData.phone}${contactData.tags.includes('Generated Phone') ? ' (generated)' : ''}`);
    console.log(`Month: ${month}`);

    return { contactId, success: true };
  } catch (error) {
    // Log detailed error information
    console.error(`[${new Date().toISOString()}] ERROR: Failed to create contact`);
    console.error(`Patient ID: ${patient.id}`);
    console.error(`Patient Name: ${cleanedPatient.first_name} ${cleanedPatient.last_name}`);
    console.error(`Email sent: ${contactData.email}${contactData.tags.includes('Generated Email') ? ' (generated)' : ''}`);
    console.error(`Phone sent: ${contactData.phone}${contactData.tags.includes('Generated Phone') ? ' (generated)' : ''}`);
    console.error(`Error message: ${error.message}`);

    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      if (error.response.data && error.response.data.message) {
        console.error(`API message: ${error.response.data.message}`);
      }
      if (error.response.data && error.response.data.errors) {
        console.error(`API errors:`, JSON.stringify(error.response.data.errors, null, 2));
      }
    }

    return { contactId: null, success: false };
  }
}

/**
 * Create an opportunity in GoHighLevel for a contact
 * @param {string} contactId - The contact ID to create an opportunity for
 * @param {Object} patient - The patient data to create an opportunity from
 * @returns {Promise<{opportunityId: string|null, success: boolean}>} - The opportunity ID if successful
 */
async function createGoHighLevelOpportunity(contactId, patient) {
  // Skip if GoHighLevel integration is not enabled or configuration is invalid
  if (!isGhlIntegrationEnabled || !isGhlConfigValid) {
    return { opportunityId: null, success: false };
  }

  // Skip if pipeline configuration is missing
  if (!GO_HIGHLEVEL_PIPELINE_ID || !GO_HIGHLEVEL_PIPELINE_STAGE_ID) {
    console.log(`Skipping opportunity creation for patient ${patient.id} due to missing pipeline configuration`);
    return { opportunityId: null, success: false };
  }

  // Skip if no contact ID was provided
  if (!contactId) {
    console.log(`Skipping opportunity creation for patient ${patient.id} due to missing contact ID`);
    return { opportunityId: null, success: false };
  }

  // Clean patient data to ensure we have valid name fields
  const cleanedPatient = cleanPatientData(patient);

  const opportunityData = {
    pipelineId: GO_HIGHLEVEL_PIPELINE_ID,
    pipelineStageId: GO_HIGHLEVEL_PIPELINE_STAGE_ID,
    locationId: GO_HIGHLEVEL_LOCATION_ID,
    name: `${cleanedPatient.first_name} ${cleanedPatient.last_name}`,
    status: "open",
    contactId,
    monetaryValue: 0,
    assignedTo: "",
    source: "DrChrono Integration"
  };

  try {
    const response = await axios.post(`${GO_HIGHLEVEL_API_URL}/opportunities/`, opportunityData, {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${GO_HIGHLEVEL_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
        Version: '2021-07-28',
      },
    });

    const opportunityId = response.data.id;

    // Log success with detailed information
    console.log(`[${new Date().toISOString()}] SUCCESS: Opportunity created successfully`);
    console.log(`Opportunity ID: ${opportunityId}`);
    console.log(`Contact ID: ${contactId}`);
    console.log(`Patient: ${cleanedPatient.first_name} ${cleanedPatient.last_name} (ID: ${patient.id})`);

    return { opportunityId, success: true };
  } catch (error) {
    // Log detailed error information
    console.error(`[${new Date().toISOString()}] ERROR: Failed to create opportunity`);
    console.error(`Patient ID: ${patient.id}`);
    console.error(`Contact ID: ${contactId}`);
    console.error(`Error message: ${error.message}`);

    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      if (error.response.data && error.response.data.message) {
        // Log error message from API without patient details
        console.error(`API message: ${error.response.data.message}`);
      }
    }

    return { opportunityId: null, success: false };
  }
}

/**
 * Fetch all patients from DrChrono API created OR updated in a specific year, organized by month
 * @param {number} year - The year to fetch patients for (default: current year)
 * @param {Array<string>} targetMonths - Array of month names to fetch (default: all months)
 */
async function fetchPatientsByMonth(year = getCurrentYear(), targetMonths = null) {
  try {
    console.log(`Fetching patients from DrChrono API created OR updated in ${year}...`);
    console.log(`This includes both new patients and returning patients with updates in ${year}.`);

    // Load the patient database for tracking counts
    console.log('Loading patient database...');
    const patientDatabase = patientDb.load();
    console.log(`Loaded database with last count: ${patientDatabase.lastCount}`);
    console.log(`Last sync: ${patientDatabase.lastSync}`);

    // Log month counts if available
    if (patientDatabase.monthCounts) {
      console.log('Month counts:');
      Object.entries(patientDatabase.monthCounts).forEach(([month, count]) => {
        console.log(`  ${month}: ${count}`);
      });
    }

    // Define all months of the year
    const allMonths = [
      { name: 'January', number: 1, days: 31 },
      { name: 'February', number: 2, days: isLeapYear(year) ? 29 : 28 },
      { name: 'March', number: 3, days: 31 },
      { name: 'April', number: 4, days: 30 },
      { name: 'May', number: 5, days: 31 },
      { name: 'June', number: 6, days: 30 },
      { name: 'July', number: 7, days: 31 },
      { name: 'August', number: 8, days: 31 },
      { name: 'September', number: 9, days: 30 },
      { name: 'October', number: 10, days: 31 },
      { name: 'November', number: 11, days: 30 },
      { name: 'December', number: 12, days: 31 }
    ];

    // Filter months based on targetMonths parameter
    let months = targetMonths
      ? allMonths.filter(month => targetMonths.includes(month.name))
      : allMonths;

    // Check which months have already been processed based on counts
    if (patientDatabase.monthCounts) {
      console.log('\n===== CHECKING PREVIOUSLY PROCESSED MONTHS =====');
      const currentDate = new Date();
      const currentMonth = allMonths[currentDate.getMonth()].name;

      // Show status of all months but process all requested months
      months.forEach(month => {
        const count = patientDatabase.monthCounts[month.name] || 0;
        const isCurrentMonth = month.name === currentMonth;

        if (count > 0) {
          console.log(`${month.name}: ${count} patients already processed${isCurrentMonth ? ' (current month - will recheck for new patients)' : ' (will recheck for new patients)'}`);
        } else {
          console.log(`${month.name}: No patients processed yet - will fetch all patients`);
        }
      });

      console.log('Note: All requested months will be processed to ensure latest contacts are synced.');
      console.log('=====================================================\n');
    }

    // Add date strings to each month
    months.forEach(month => {
      const monthStr = month.number.toString().padStart(2, '0');
      month.startDate = `${year}-${monthStr}-01`;
      month.endDate = `${year}-${monthStr}-${month.days}`;
    });

    // Object to store patients by month
    let patientsByMonth = {};

    // Object to store log data
    let logData = {
      lastUpdated: new Date().toISOString(),
      months: {},
      totals: { found: 0, processed: 0, ready_for_ghl: 0 },
      integration: {
        name: 'DrChrono to GoHighLevel',
        version: '1.0.0',
        source: 'DrChrono',
        destination: 'GoHighLevel',
        year: year,
        targetMonths: targetMonths || 'All Months'
      }
    };

    // Initialize the patientsByMonth and logData objects
    months.forEach(month => {
      patientsByMonth[month.name] = [];
      const monthKey = month.startDate.substring(0, 7); // Format: YYYY-MM
      logData.months[monthKey] = {
        found: 0,
        processed: 0,
        ready_for_ghl: 0,
        patients: []
      };
    });

    // Function to log processing statistics without saving patient data
    function logProcessingStats() {
      // Log only aggregate statistics without saving any patient data
      console.log(`Processing statistics updated. Total patients processed: ${logData.totals.processed}`);
    }

    // Fetch ALL patients from the beginning of the year first, then sort by month
    console.log(`\n===== FETCHING ALL PATIENTS FROM JANUARY 1, ${year} =====`);
    console.log('This approach ensures we get ALL patients (new and returning) and then sort them correctly by month.');
    console.log('We will process patients who were either CREATED or UPDATED in the current year.');

    let allPatientsFromYear = [];
    let totalPagesProcessed = 0;

    // Make the initial API request to get ALL patients from the beginning of the year
    console.log(`Making initial API request to fetch all patients from ${year}...`);
    const initialResponse = await axios.get('https://app.drchrono.com/api/patients', {
      params: {
        page_size: 200, // Use reasonable page size for better performance
        since: `${year}-01-01`, // Get patients created on or after January 1st of the target year
        verbose: true // Include additional data
      },
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    console.log(`Initial API call successful!`);
    console.log(`Total patients in first page: ${initialResponse.data.results.length}`);

    // Add patients from first page
    allPatientsFromYear = allPatientsFromYear.concat(initialResponse.data.results);
    totalPagesProcessed = 1;

    // Fetch all remaining pages to get ALL patients
    let nextUrl = initialResponse.data.next;
    while (nextUrl) {
      totalPagesProcessed++;
      console.log(`Fetching page ${totalPagesProcessed}...`);

      const response = await axios.get(nextUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      console.log(`Page ${totalPagesProcessed} fetched successfully! Results: ${response.data.results.length}`);

      // Add patients from this page
      allPatientsFromYear = allPatientsFromYear.concat(response.data.results);

      // Update the next URL for pagination
      nextUrl = response.data.next;
    }

    console.log(`\nCompleted fetching ALL patients!`);
    console.log(`Total pages processed: ${totalPagesProcessed}`);
    console.log(`Total patients retrieved: ${allPatientsFromYear.length}`);

    // Now process and sort patients by month
    console.log(`\n===== SORTING PATIENTS BY MONTH =====`);

    // Process each month separately using the fetched patients
    for (const month of months) {
      const monthKey = month.startDate.substring(0, 7); // Format: YYYY-MM
      console.log(`\n===== PROCESSING PATIENTS FOR ${month.name.toUpperCase()} ${year} =====`);

      // Filter patients for this specific month from all fetched patients
      const patients = [];

      // Initialize a map to track patients by their actual creation month for this processing
      const patientsByActualMonth = {};

      // Process each patient from the complete dataset
      allPatientsFromYear.forEach(patient => {
        // Check if patient should be processed (created OR updated in target year)
        const processResult = shouldProcessPatient(patient, year);

        if (!processResult.shouldProcess) return;

        // Get the month name from the primary date (created_at for new patients, updated_at for returning)
        const monthName = getMonthNameFromDate(processResult.primaryDate.toISOString());

        // Track all patients by their actual month for logging
        if (!patientsByActualMonth[monthName]) {
          patientsByActualMonth[monthName] = 0;
        }
        patientsByActualMonth[monthName]++;

        // If this is the month we're currently processing, add to the patients array
        if (monthName === month.name) {
          // Add processing reason to patient object for logging
          patient._processReason = processResult.reason;
          patient._primaryDate = processResult.primaryDate.toISOString();
          patients.push(patient);
        }
      });

      // Log the breakdown of patients by month (for this processing cycle)
      console.log(`\nPatient breakdown from complete dataset:`);
      Object.entries(patientsByActualMonth).forEach(([monthName, count]) => {
        if (count > 0) {
          console.log(`  ${monthName}: ${count} patients`);
        }
      });

      console.log(`\nFiltered patients for ${month.name}: ${patients.length}`);

      // Log breakdown by patient type for this month
      if (patients.length > 0) {
        const newPatients = patients.filter(p => p._processReason === 'new_patient' || p._processReason === 'new_and_updated').length;
        const returningPatients = patients.filter(p => p._processReason === 'returning_patient').length;
        console.log(`  - New patients: ${newPatients}`);
        console.log(`  - Returning patients: ${returningPatients}`);
      }

      patientsByMonth[month.name] = patientsByMonth[month.name].concat(patients);

      // Update log data
      logData.months[monthKey].found += patients.length;
      logData.totals.found += patients.length;

      // Process each patient for logging
      for (const patient of patients) {
        // Clean and normalize patient data
        const cleanedPatient = cleanPatientData(patient);

        // Format patient data for GoHighLevel compatibility
        const contactData = formatPatientForGoHighLevel(cleanedPatient);

        // Add patient to the log with minimal information and GoHighLevel format
        const patientInfo = {
          id: patient.id,
          chart_id: patient.chart_id,
          first_name: cleanedPatient.first_name,
          last_name: cleanedPatient.last_name,
          date_of_birth: cleanedPatient.date_of_birth,
          email: cleanedPatient.email,
          cell_phone: cleanedPatient.cell_phone,
          created_at: patient.created_at,
          updated_at: patient.updated_at,
          process_reason: patient._processReason || 'unknown',
          primary_date: patient._primaryDate || patient.created_at,
          processed: true,
          ghl_contact_data: contactData, // Store the GoHighLevel formatted data
          ghl_contact_id: null,
          ghl_processed: false,
          ghl_opportunity_id: null
        };

        // Add to the month's patient list
        logData.months[monthKey].patients.push(patientInfo);

        // Increment processed count
        logData.months[monthKey].processed++;
        logData.totals.processed++;

        // Check if patient has valid contact information for GoHighLevel
        if (isValidForGoHighLevel(contactData)) {
          logData.months[monthKey].ready_for_ghl++;
          logData.totals.ready_for_ghl++;

          // Create contact in GoHighLevel if integration is enabled and configuration is valid
          if (isGhlIntegrationEnabled && isGhlConfigValid) {
            try {
              // Pass the patient database and month to the contact creation function
              const result = await createGoHighLevelContact(patient, patientDatabase, month.name);
              const contactId = result.contactId;
              const contactSuccess = result.success;

              // Create opportunity if contact was created successfully
              if (contactId && contactSuccess) {
                const oppResult = await createGoHighLevelOpportunity(contactId, patient);
                const opportunityId = oppResult.opportunityId;
                const oppSuccess = oppResult.success;

                // Update patient info with GoHighLevel data
                patientInfo.ghl_contact_id = contactId;
                patientInfo.ghl_processed = true;
                if (opportunityId && oppSuccess) {
                  patientInfo.ghl_opportunity_id = opportunityId;
                }

                // Update the count for this month
                const currentCount = patientDb.getMonthCount(patientDatabase, month.name);
                patientDatabase = patientDb.updateMonthCount(patientDatabase, month.name, currentCount + 1);
                patientDb.save(patientDatabase);
              }
            } catch (error) {
              console.error(`Failed to create GoHighLevel contact/opportunity for patient ${patient.id}`);
              console.error(`Error: ${error.message}`);
            }
          }
        }
      }

      // Log processing statistics without saving patient data
      logProcessingStats();

      console.log(`\nCompleted processing ${month.name}!`);
      console.log(`Total patients created in ${month.name} ${year}: ${patientsByMonth[month.name].length}`);
    }

    console.log(`\nAll months processed successfully!`);

    // Calculate total patients across all months
    let totalPatients = 0;
    for (const month in patientsByMonth) {
      totalPatients += patientsByMonth[month].length;
    }

    console.log(`Total patients processed for ${year}: ${totalPatients}`);

    // Log the total number of patients processed
    console.log('\n===== SUMMARY OF PATIENTS PROCESSED =====');
    console.log(`Total patients processed: ${totalPatients}`);

    // Calculate totals for GoHighLevel
    const totalGhlProcessed = Object.values(logData.months).reduce((sum, data) =>
      sum + data.patients.filter(p => p.ghl_processed).length, 0);
    const totalGhlOpportunities = Object.values(logData.months).reduce((sum, data) =>
      sum + data.patients.filter(p => p.ghl_opportunity_id).length, 0);

    // Log the data found and processed (totals only)
    console.log('\n===== DATA PROCESSING SUMMARY =====');
    console.log(`TOTAL: Found ${logData.totals.found}, Processed ${logData.totals.processed}, Ready for GHL ${logData.totals.ready_for_ghl}, GHL Contacts ${totalGhlProcessed}, GHL Opportunities ${totalGhlOpportunities}`);

    // Log the percentage of patients ready for GoHighLevel
    const readyPercentage = logData.totals.processed > 0
      ? ((logData.totals.ready_for_ghl / logData.totals.processed) * 100).toFixed(2)
      : 0;
    console.log(`\nPercentage of processed patients ready for GoHighLevel: ${readyPercentage}%`);

    // Log GoHighLevel integration statistics
    if (isGhlIntegrationEnabled) {
      const ghlSuccessRate = logData.totals.ready_for_ghl > 0
        ? ((totalGhlProcessed / logData.totals.ready_for_ghl) * 100).toFixed(2)
        : 0;
      const opportunityRate = totalGhlProcessed > 0
        ? ((totalGhlOpportunities / totalGhlProcessed) * 100).toFixed(2)
        : 0;

      console.log('\n===== GOHIGHLEVEL INTEGRATION STATISTICS =====');
      console.log(`GHL Contact Creation Success Rate: ${ghlSuccessRate}%`);
      console.log(`GHL Opportunity Creation Success Rate: ${opportunityRate}%`);

      if (!isGhlConfigValid) {
        console.log('\nWARNING: GoHighLevel integration is enabled but configuration is invalid.');
        console.log('Please check your environment variables and try again.');
      }
    }

    // Don't save patient data to files for privacy reasons
    console.log('\nPatient data not saved to disk for privacy reasons.');

    // If you need to save non-sensitive aggregate statistics, you can do so here
    // For example, saving only counts and success rates without any patient data

    return patientsByMonth;
  } catch (error) {
    console.error('Failed to fetch patients.');
    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      // Don't log response data as it might contain patient information

      // Check for authentication errors
      if (error.response.status === 401) {
        console.error('\nAuthentication error. Your token may be expired.');
        console.error('Please run refresh-token.js to get a new access token.');
      }
    } else {
      console.error(`Error: ${error.message}`);
    }
    throw error;
  }
}

// Define API routes
app.get('/', (_req, res) => {
  res.json({
    status: 'ok',
    message: 'DrChrono to GoHighLevel integration service is running',
    version: '1.0.0'
  });
});

// Health check endpoint for Render
app.get('/health', (_req, res) => {
  res.status(200).json({ status: 'healthy' });
});

// API endpoint to trigger data sync
app.post('/api/sync', async (_req, res) => {
  try {
    // Start the sync process in the background
    syncInProgress = true;

    // Send immediate response
    res.json({
      status: 'started',
      message: 'Data sync started successfully',
      startedAt: new Date().toISOString()
    });

    // Run the sync process
    await runSync();
  } catch (error) {
    console.error('Sync API error:', error.message);
    // Response already sent, just log the error
  }
});

// API endpoint to get sync status
app.get('/api/status', (_req, res) => {
  // Get the patient database for stats
  const patientDatabase = patientDb.load();

  res.json({
    status: syncInProgress ? 'in_progress' : 'idle',
    lastSync: lastSyncTime || patientDatabase.lastSync,
    stats: {
      totalProcessed: patientDatabase.lastCount,
      monthCounts: patientDatabase.monthCounts || {}
    }
  });
});

// Track sync status
let syncInProgress = false;
let lastSyncTime = null;

// Execute the sync process
async function runSync() {
  try {
    console.log('Starting data sync process...');

    // Load and validate the DrChrono access token
    accessToken = await loadDrChronoToken();

    // Get the current month
    const currentDate = new Date();
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const currentMonthIndex = currentDate.getMonth();
    const currentMonth = monthNames[currentMonthIndex];
    const year = getCurrentYear(); // Use dynamic current year

    // Determine which months to process based on configuration
    let targetMonths;
    if (fetchFromBeginningOfYear) {
      // Process all months from January to current month
      targetMonths = monthNames.slice(0, currentMonthIndex + 1);
      console.log(`\n===== PROCESSING ALL MONTHS FROM JANUARY TO ${currentMonth.toUpperCase()} ${year} =====`);
      console.log(`Target months: ${targetMonths.join(', ')}`);
      console.log(`This ensures all contacts (new and returning) from the beginning of ${year} are synced.`);
      console.log('=======================================================================\n');
    } else {
      // Only process the current month
      targetMonths = [currentMonth];
      console.log(`\n===== PROCESSING ONLY CURRENT MONTH: ${currentMonth.toUpperCase()} ${year} =====`);
      console.log('This makes the script more efficient by only querying for the current month.');
      console.log(`Set FETCH_FROM_BEGINNING_OF_YEAR=true to fetch from January ${year}.`);
      console.log('=========================================================\n');
    }

    await fetchPatientsByMonth(year, targetMonths);

    console.log('\nSync completed successfully!');
    if (fetchFromBeginningOfYear) {
      console.log(`Processed patients (new and returning) for January through ${currentMonth} ${year}`);
    } else {
      console.log(`Processed patients (new and returning) for ${currentMonth} ${year}`);
    }
    console.log('Patient counts saved to patient_db.json');

    // Update sync status
    lastSyncTime = new Date().toISOString();
    syncInProgress = false;

    return true;
  } catch (error) {
    console.error('Sync failed:', error.message);
    syncInProgress = false;
    return false;
  }
}

// Start the Express server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check available at http://localhost:${PORT}/health`);

  // Schedule the cron job to run every 10 minutes
  cron.schedule('*/10 * * * *', async () => {
    console.log(`\n[${new Date().toISOString()}] Running scheduled sync...`);

    // Skip if a sync is already in progress
    if (syncInProgress) {
      console.log('Skipping scheduled sync as another sync is already in progress');
      return;
    }

    // Run the sync
    syncInProgress = true;
    await runSync();
  });

  // Run initial sync on startup
  console.log('Running initial sync on startup...');
  syncInProgress = true;
  runSync();
});
