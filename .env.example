# DrChrono API Configuration
DRCHRONO_CLIENT_ID=your_client_id
DRCHRONO_CLIENT_SECRET=your_client_secret
DRCHRONO_REDIRECT_URI=http://localhost:3000/oauth/callback
DRCHRONO_API_URL=https://app.drchrono.com/api

# GoHighLevel API Configuration for Private Integration
# For private integrations, you need to use the API key from GoHighLevel
# This can be found in your GoHighLevel account under Settings > API Keys

# Location Configuration
# Example for a location/practice
LOCATION_1_NAME=Practice Name
LOCATION_1_ACCESS_TOKEN=your_gohighlevel_api_key  # API key from GoHighLevel
LOCATION_1_LOCATION_ID=your_location_id  # Location ID from GoHighLevel
LOCATION_1_PIPELINE_ID=your_pipeline_id  # Optional: Pipeline ID if you want to create opportunities
LOCATION_1_PIPELINE_STAGE_ID=your_pipeline_stage_id  # Optional: Pipeline Stage ID

# Application Configuration
PORT=3000
LOG_LEVEL=info
CRON_SCHEDULE=*/30 * * * * # Every 30 minutes
RETRY_CRON_SCHEDULE=*/5 * * * * # Every 5 minutes
FULL_SYNC_CRON_SCHEDULE=0 2 * * * # Daily at 2 AM
VERIFICATION_CRON_SCHEDULE=0 3 * * 0 # Weekly on Sunday at 3 AM

# Data Fetching Configuration
FETCH_FROM_BEGINNING_OF_YEAR=true # Set to true to fetch from January 2025, false for current month only

# GoHighLevel Integration
ENABLE_GHL_INTEGRATION=true # Set to true to enable GoHighLevel integration
